# Frontend Docker ignore file
# Exclude unnecessary files from Docker build context

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next/
out/
build/
dist/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Testing
test/
tests/
__tests__/
*.test.js
*.test.ts
*.test.jsx
*.test.tsx
*.spec.js
*.spec.ts
*.spec.jsx
*.spec.tsx

# Storybook
.storybook/
storybook-static/

# Documentation
docs/
README.md
CHANGELOG.md

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Vercel
.vercel

# Next.js
.next/
out/

# Temporary folders
tmp/
temp/
