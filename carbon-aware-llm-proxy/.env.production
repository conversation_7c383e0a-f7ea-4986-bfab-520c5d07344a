# Production Environment Configuration
# Copy this file to .env for production deployment

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD
DB_NAME=carbon_aware_llm

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration (MUST CHANGE IN PRODUCTION)
JWT_SECRET=CHANGE_THIS_TO_A_SECURE_RANDOM_STRING_IN_PRODUCTION
JWT_EXPIRES_IN=7d

# API Configuration
PORT=3001
NODE_ENV=production

# CORS Configuration (Update with your production domains)
CORS_ORIGINS=https://your-domain.com,https://api.your-domain.com

# Logging
LOG_LEVEL=warn

# External API Keys (Required for full functionality)
WATT_TIME_EMAIL=<EMAIL>
WATT_TIME_API_KEY=your-watttime-api-key
ELECTRICITY_MAP_API_KEY=your-electricity-map-api-key

# Frontend Configuration (Update with your production API URL)
NEXT_PUBLIC_API_URL=https://api.your-domain.com

# Feature Flags
ENABLE_RATE_LIMITING=true
ENABLE_CARBON_ANALYTICS=true

# Production Settings
NEXT_TELEMETRY_DISABLED=1

# Security Settings (Optional)
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem
