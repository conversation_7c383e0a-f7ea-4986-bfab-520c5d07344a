# Development Docker Compose Configuration
# For local development with hot reload

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: carbon-aware-postgres-dev
    environment:
      POSTGRES_DB: ${DB_NAME:-carbon_aware_llm}
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - carbon-aware-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-postgres} -d ${DB_NAME:-carbon_aware_llm}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: carbon-aware-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - carbon-aware-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Backend API Service
  backend:
    build:
      context: ./packages/backend
      dockerfile: Dockerfile.dev
    container_name: carbon-aware-backend-dev
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: ${DB_USERNAME:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      DB_NAME: ${DB_NAME:-carbon_aware_llm}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-dev-jwt-secret-key}
      CORS_ORIGINS: http://localhost:3000,http://localhost:3001
      LOG_LEVEL: debug
    ports:
      - "3001:3001"
    volumes:
      - ./packages/backend/src:/app/src:ro
      - ./packages/backend/package.json:/app/package.json:ro
      - ./packages/backend/tsconfig.json:/app/tsconfig.json:ro
    networks:
      - carbon-aware-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ./packages/backend/src
          target: /app/src
        - action: rebuild
          path: ./packages/backend/package.json

  # Frontend Service
  frontend:
    build:
      context: ./packages/frontend
      dockerfile: Dockerfile.dev
    container_name: carbon-aware-frontend-dev
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001
      NEXT_TELEMETRY_DISABLED: 1
    ports:
      - "3000:3000"
    volumes:
      - ./packages/frontend/src:/app/src:ro
      - ./packages/frontend/public:/app/public:ro
      - ./packages/frontend/package.json:/app/package.json:ro
      - ./packages/frontend/next.config.js:/app/next.config.js:ro
      - ./packages/frontend/tailwind.config.js:/app/tailwind.config.js:ro
      - ./packages/frontend/postcss.config.js:/app/postcss.config.js:ro
      - ./packages/frontend/tsconfig.json:/app/tsconfig.json:ro
    networks:
      - carbon-aware-network
    depends_on:
      - backend
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ./packages/frontend/src
          target: /app/src
        - action: rebuild
          path: ./packages/frontend/package.json

networks:
  carbon-aware-network:
    driver: bridge
    name: carbon-aware-network

volumes:
  postgres_data_dev:
    name: carbon-aware-postgres-data-dev
  redis_data_dev:
    name: carbon-aware-redis-data-dev
