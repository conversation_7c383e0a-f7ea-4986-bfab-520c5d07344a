# Docker Environment Configuration
# Copy this file to .env and customize for your environment

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=carbon_aware_llm

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration (comma-separated origins)
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Logging
LOG_LEVEL=info

# External API Keys (Optional - for carbon tracking features)
WATT_TIME_EMAIL=
WATT_TIME_API_KEY=
ELECTRICITY_MAP_API_KEY=

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001

# Feature Flags
ENABLE_RATE_LIMITING=true
ENABLE_CARBON_ANALYTICS=true

# Development Settings
NEXT_TELEMETRY_DISABLED=1
