# Production Docker Compose Configuration
# Optimized for production deployment

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: carbon-aware-postgres-prod
    environment:
      POSTGRES_DB: ${DB_NAME:-carbon_aware_llm}
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - carbon-aware-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-postgres} -d ${DB_NAME:-carbon_aware_llm}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: carbon-aware-redis-prod
    volumes:
      - redis_data_prod:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - carbon-aware-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always
    command: redis-server /usr/local/etc/redis/redis.conf
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Backend API Service
  backend:
    build:
      context: ./packages/backend
      dockerfile: Dockerfile
    container_name: carbon-aware-backend-prod
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: ${DB_USERNAME:-postgres}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_NAME: ${DB_NAME:-carbon_aware_llm}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      WATT_TIME_EMAIL: ${WATT_TIME_EMAIL}
      WATT_TIME_API_KEY: ${WATT_TIME_API_KEY}
      ELECTRICITY_MAP_API_KEY: ${ELECTRICITY_MAP_API_KEY}
    networks:
      - carbon-aware-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Frontend Service
  frontend:
    build:
      context: ./packages/frontend
      dockerfile: Dockerfile
    container_name: carbon-aware-frontend-prod
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:3001}
      NEXT_TELEMETRY_DISABLED: 1
    networks:
      - carbon-aware-network
    depends_on:
      - backend
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: carbon-aware-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
    networks:
      - carbon-aware-network
    depends_on:
      - frontend
      - backend
    restart: always
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

networks:
  carbon-aware-network:
    driver: bridge
    name: carbon-aware-network-prod

volumes:
  postgres_data_prod:
    name: carbon-aware-postgres-data-prod
  redis_data_prod:
    name: carbon-aware-redis-data-prod
